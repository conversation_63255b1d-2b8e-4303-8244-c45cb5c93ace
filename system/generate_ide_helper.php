<?php
/**
 * IDE Helper Generator
 * 
 * This script generates an IDE helper file for dynamically created constants
 * to help IDEs recognize them and suppress false "undefined constant" errors.
 * 
 * Usage:
 *   php system/generate_ide_helper.php
 *   php system/generate_ide_helper.php --force
 * 
 * Options:
 *   --force    Force regeneration even if file exists and is recent
 *   --help     Show this help message
 */

// Check for command line arguments
$options = getopt('', ['force', 'help']);

if (isset($options['help'])) {
    echo "IDE Helper Generator\n";
    echo "===================\n\n";
    echo "Generates an IDE helper file for dynamically created constants.\n\n";
    echo "Usage:\n";
    echo "  php system/generate_ide_helper.php [options]\n\n";
    echo "Options:\n";
    echo "  --force    Force regeneration even if file exists and is recent\n";
    echo "  --help     Show this help message\n\n";
    echo "The generated file will be saved as '_ide_helper_constants.php' in the\n";
    echo "application root directory.\n\n";
    exit(0);
}

// Set up the environment
$path = [];
$path['fs_app_root'] = str_replace("system", "", __DIR__);

// Include the paths file to get the functions
include($path['fs_app_root'] . "system/paths.php");

// Load the path schema
$schema_file = $path['fs_app_root'] . 'system/config/path_schema.php';
$schema = file_exists($schema_file) ? include($schema_file) : [];

echo "IDE Helper Generator\n";
echo "===================\n\n";

// Check if we should regenerate
$helper_file = $path['fs_app_root'] . '_ide_helper_constants.php';
$force = isset($options['force']);

if (!$force && file_exists($helper_file)) {
    $file_age = time() - filemtime($helper_file);
    if ($file_age < 3600) { // Less than 1 hour old
        echo "✓ IDE helper file exists and is recent (generated " . 
             round($file_age / 60) . " minutes ago)\n";
        echo "  Use --force to regenerate anyway\n\n";
        exit(0);
    }
}

echo "Generating IDE helper file...\n";

// Build the paths to get all constants
$path = build_paths($path, $schema);

// Simulate the constants that would be created
$constants_defined = [];
foreach ($path as $key => $value) {
    $constant_name = strtoupper($key);
    if (is_string($value)) {
        // Apply the same normalization as build_constants
        $is_absolute = str_starts_with($value, '/');
        $has_trailing_slash = str_ends_with($value, '/');
        $normalized = normalize_path($value);
        $value = ($is_absolute ? '/' : '') . $normalized . ($has_trailing_slash ? '/' : '');
    }
    
    $constants_defined[$constant_name] = [
        'value' => $value,
        'type' => gettype($value),
        'source_key' => $key
    ];
}

// Load additional constants from definitions
$definitions_file = $path['fs_app_root'] . 'system/config/path_definitions.php';
$definitions = file_exists($definitions_file) ? include($definitions_file) : [];
$additional_constants = $definitions['additional_constants'] ?? [];

// Convert additional constants to the expected format and add descriptions
$formatted_additional = [];
foreach ($additional_constants as $name => $config) {
    $formatted_additional[$name] = [
        'value' => $config['value'],
        'type' => $config['type'],
        'source_key' => $config['source_key']
    ];

    // Add the description to the definitions for this constant
    if (isset($config['description'])) {
        $definitions['descriptions'][$name] = $config['description'];
    }
}

$constants_defined = array_merge($constants_defined, $formatted_additional);

// Generate the IDE helper file
generate_ide_helper_constants($constants_defined);

echo "✓ IDE helper file generated: " . basename($helper_file) . "\n";
echo "✓ Total constants documented: " . count($constants_defined) . "\n\n";

echo "To use this in your IDE:\n";
echo "1. The file is automatically included when constants are built\n";
echo "2. Your IDE should now recognize all dynamically generated constants\n";
echo "3. Re-run this script when you add new constants or paths\n\n";

echo "Note: Add '_ide_helper_constants.php' to your .gitignore if you don't\n";
echo "want to commit the generated file to version control.\n\n";
?>
